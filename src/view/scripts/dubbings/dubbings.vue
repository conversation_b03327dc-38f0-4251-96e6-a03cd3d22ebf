<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule"
        @keyup.enter="onSubmit">
        <el-form-item label="配音ID" prop="id">
          <el-input v-model.number="searchInfo.id" placeholder="搜索条件" />
        </el-form-item>

        <el-form-item label="剧本ID" prop="script_id">
          <el-input v-model.number="searchInfo.script_id" placeholder="搜索条件" />
        </el-form-item>

        <el-form-item label="剧本标题" prop="script_title">
          <el-input v-model="searchInfo.script_title" placeholder="搜索条件" />
        </el-form-item>

        <el-form-item label="台词ID" prop="line_id">
          <el-input v-model.number="searchInfo.line_id" placeholder="搜索条件" />
        </el-form-item>

        <el-form-item label="用户ID" prop="user_id">
          <el-input v-model.number="searchInfo.user_id" placeholder="搜索条件" />
        </el-form-item>

        <el-form-item v-if="!isCreator" label="审核状态" prop="review_status">
          <el-select v-model="searchInfo.review_status" clearable filterable placeholder="请选择"
            @clear="() => { searchInfo.review_status = undefined }">
            <el-option v-for="(item, key) in reviewStatusOptionsRef" :key="key" :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="配音状态" prop="status">
          <el-select v-model="searchInfo.status" clearable filterable placeholder="请选择"
            @clear="() => { searchInfo.status = undefined }">
            <el-option v-for="(item, key) in statusOptionsRef" :key="key" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间" prop="created_at">
          <el-date-picker v-model="searchInfo.startCreatedAt" type="datetime" placeholder="开始时间"
            format="YYYY-MM-DD HH:mm:ss" value-format="x" style="width:200px" />
          —
          <el-date-picker v-model="searchInfo.endCreatedAt" type="datetime" placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss" value-format="x" style="width:200px" />
        </el-form-item>

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery = true"
            v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery = false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button v-if="!isCreator" icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length"
          @click="onDelete">删除</el-button>
      </div>
      <el-table ref="multipleTable" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="id"
        @selection-change="handleSelectionChange" @sort-change="sortChange">
        <el-table-column type="selection" width="55" />

        <el-table-column align="left" label="配音ID" prop="id" min-width="80" />

        <el-table-column align="left" label="用户ID" prop="user_id" min-width="80" />

        <!-- 剧本信息整合列 -->
        <el-table-column align="left" label="剧本信息" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            <div class="script-info">
              <div class="script-id">
                ID: {{ scope.row.script_id }}
              </div>
              <div class="script-title">
                {{ scope.row.script_title || '未知标题' }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 原声配音列 -->
        <el-table-column align="left" label="原声配音" min-width="300" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.row.original_url" class="single-audio-wrapper original-audio">
              <AudioPlayer :src="scope.row.original_url" :duration="scope.row.original_duration" />
            </div>
            <div v-else class="no-audio-content">
              <span class="no-audio-text">无原声音频</span>
            </div>
          </template>
        </el-table-column>

        <!-- 变声配音列 -->
        <el-table-column align="left" label="变声配音" min-width="300" show-overflow-tooltip>
          <template #default="scope">
            <div v-if="scope.row.dubbed_url" class="single-audio-wrapper dubbed-audio">
              <AudioPlayer :src="scope.row.dubbed_url" :duration="scope.row.dubbed_duration" />
            </div>
            <div v-else class="no-audio-content">
              <span class="no-audio-text">无变声音频</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column align="left" label="台词ID" prop="line_id" min-width="80" />

        <el-table-column align="left" label="角色ID" prop="character_id" min-width="80" />

        <!-- 作者标识列 -->
        <el-table-column align="left" label="作者" min-width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.is_author" size="small" type="warning">
              作者
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 点赞数可编辑列 -->
        <el-table-column align="left" label="点赞数" min-width="90">
          <template #default="scope">
            <div class="likes-input">
              <el-input-number
                v-model="scope.row.likes"
                :min="0"
                size="small"
                @change="handleLikesChange(scope.row, $event)"
                controls-position="right"
              />
            </div>
          </template>
        </el-table-column>

        <!-- 置顶开关列 -->
        <el-table-column align="left" label="置顶" min-width="60">
          <template #default="scope">
            <!-- 只有状态正常且未被拒绝的配音才显示置顶开关 -->
            <el-switch
              v-if="canSetTop(scope.row)"
              v-model="scope.row.is_top"
              @change="handleTopChange(scope.row, $event)"
              size="small"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 审核状态列 -->
        <el-table-column v-if="!isCreator" sortable align="left" label="审核状态" prop="review_status" min-width="130">
          <template #default="scope">
            <el-select v-model="scope.row.review_status" placeholder="审核状态" size="small" style="width: 120px;"
              @change="handleReviewStatusChange(scope.row, $event)">
              <el-option v-for="(item, key) in reviewStatusOptionsRef" :key="key" :label="item.label"
                :value="item.value" />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column sortable align="left" label="配音状态" prop="status" min-width="90">
          <template #default="scope">
            {{ filterDict(scope.row.status, statusOptionsRef) || '-' }}
          </template>
        </el-table-column>

        <el-table-column sortable align="left" label="创建时间" prop="created_at" min-width="150">
          <template #default="scope">
            {{ formatTimestamp(scope.row.created_at) }}
          </template>
        </el-table-column>

        <!-- 操作列后置 -->
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
          <template #default="scope">
            <div class="action-buttons">
              <!-- 角色999（审核员）显示审核按钮 -->
              <template v-if="isAuditor">
                <el-button
                  type="success"
                  link
                  size="small"
                  @click="handleManualApprove(scope.row)"
                >
                  人审通过
                </el-button>
                <el-button
                  type="danger"
                  link
                  size="small"
                  @click="handleManualReject(scope.row)"
                >
                  人审拒绝
                </el-button>
              </template>
              <!-- 非审核员显示常规操作按钮 -->
              <template v-else>
                <el-button type="primary" link size="small" @click="getDetails(scope.row)">查看</el-button>
                <el-button type="primary" link size="small" @click="updateDubbingFunc(scope.row)">编辑</el-button>
                <el-button v-if="!isCreator" type="danger" link size="small" @click="deleteRow(scope.row)">删除</el-button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="page" :page-size="pageSize"
          :page-sizes="[20, 30, 50, 100]" :total="total" @current-change="handleCurrentChange"
          @size-change="handleSizeChange" />
      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false"
      :before-close="closeDialog">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : type === 'update' ? '编辑' : '查看' }}配音</span>
          <div>
            <el-button v-if="type !== 'detail'" :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </div>
      </template>

      <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
        <el-form-item label="剧本ID:" prop="script_id">
          <el-input v-model.number="formData.script_id" :clearable="true" placeholder="请输入剧本ID" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="台词ID:" prop="line_id">
          <el-input v-model.number="formData.line_id" :clearable="true" placeholder="请输入台词ID" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="话题ID:" prop="topic_id">
          <el-input v-model.number="formData.topic_id" :clearable="true" placeholder="请输入话题ID" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="用户ID:" prop="user_id">
          <el-input v-model.number="formData.user_id" :clearable="true" placeholder="请输入用户ID" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="角色ID:" prop="character_id">
          <el-input v-model.number="formData.character_id" :clearable="true" placeholder="请输入角色ID" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="角色资源ID:" prop="character_asset_id">
          <el-input v-model.number="formData.character_asset_id" :clearable="true" placeholder="请输入角色资源ID" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="原声地址:" prop="original_url">
          <el-input v-model="formData.original_url" :clearable="true" placeholder="请输入原声地址" :disabled="type === 'detail'" />
          <div v-if="formData.original_url" class="form-audio-player">
            <AudioPlayer :src="formData.original_url" />
          </div>
        </el-form-item>

        <el-form-item label="原声时长(毫秒):" prop="original_duration">
          <el-input-number v-model="formData.original_duration" :min="0" :clearable="true" placeholder="请输入原声时长"
            style="width:100%" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="配音地址:" prop="dubbed_url">
          <el-input v-model="formData.dubbed_url" :clearable="true" placeholder="请输入配音地址" :disabled="type === 'detail'" />
          <div v-if="formData.dubbed_url" class="form-audio-player">
            <AudioPlayer :src="formData.dubbed_url" />
          </div>
        </el-form-item>

        <el-form-item label="配音时长(毫秒):" prop="dubbed_duration">
          <el-input-number v-model="formData.dubbed_duration" :min="0" :clearable="true" placeholder="请输入配音时长"
            style="width:100%" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="是否作者配音:" prop="is_author">
          <el-switch v-model="formData.is_author" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="是否置顶:" prop="is_top">
          <el-switch v-model="formData.is_top" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item label="点赞数:" prop="likes">
          <el-input-number v-model="formData.likes" :min="0" :clearable="true" placeholder="请输入点赞数"
            style="width:100%" :disabled="type === 'detail'" />
        </el-form-item>

        <el-form-item v-if="!isCreator" label="审核状态:" prop="review_status">
          <el-select v-model="formData.review_status" placeholder="请选择审核状态" style="width:100%" filterable
            :clearable="true" :disabled="type === 'detail'">
            <el-option v-for="(item, key) in reviewStatusOptionsRef" :key="key" :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="配音状态:" prop="status">
          <el-select v-model="formData.status" placeholder="请选择配音状态" style="width:100%" filterable
            :clearable="true" :disabled="type === 'detail'">
            <el-option v-for="(item, key) in statusOptionsRef" :key="key" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup>
import {
  createDubbing,
  deleteDubbing,
  deleteDubbingByIds,
  updateDubbing,
  findDubbing,
  getDubbingList,
  auditDubbing,
  setDubbingTop
} from '@/api/scripts/dubbings'

// 全量引入格式化工具
import { formatTimestamp, filterDict } from '@/utils/format'
import {
  reviewStatusOptions,
  getDubbingStatusText
} from '@/constants/scriptStatus'
import AudioPlayer from '@/components/AudioPlayer/AudioPlayer.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, computed } from 'vue'
import { useAppStore, useUserStore } from "@/pinia"

defineOptions({
  name: 'DubbingList'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()
const userStore = useUserStore()

// 计算当前用户是否为角色999（审核员）
const isAuditor = computed(() => {
  return userStore.userInfo?.authority?.authorityId === 999
})



// 计算当前用户是否为角色111（创作者）
const isCreator = computed(() => {
  return userStore.userInfo?.authority?.authorityId === 111
})

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 状态选项
const statusOptionsRef = ref([
  { label: '正常', value: 1 },
  { label: '删除', value: 2 }
])
const reviewStatusOptionsRef = ref(reviewStatusOptions)

const formData = ref({
  id: undefined,
  script_id: undefined,
  line_id: undefined,
  topic_id: undefined,
  user_id: undefined,
  character_id: undefined,
  character_asset_id: undefined,
  original_url: '',
  original_duration: undefined,
  dubbed_url: '',
  dubbed_duration: undefined,
  is_author: false,
  is_top: false,
  likes: undefined,
  review_status: undefined,
  status: undefined,
  created_at: undefined,
  updated_at: undefined,
})

// 验证规则
const rule = reactive({})

const searchRule = reactive({
  CreatedAt: [
    {
      validator: (rule, value, callback) => {
        if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
          callback(new Error('请填写结束日期'))
        } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
          callback(new Error('请填写开始日期'))
        } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
          callback(new Error('开始日期应当早于结束日期'))
        } else {
          callback()
        }
      }, trigger: 'change'
    }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(20)
const tableData = ref([])
const searchInfo = ref({})

// 排序
const sortChange = ({ prop, order }) => {
  const sortMap = {
    review_status: 'review_status',
    status: 'status',
    created_at: 'created_at',
  }

  let sort = sortMap[prop]
  if (!sort) {
    sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
  }

  searchInfo.value.sort = sort
  searchInfo.value.order = order
  getTableData()
}

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async (valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 20
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async () => {
  const table = await getDubbingList({
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo.value,
  })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 此处可根据需求修改
const setOptions = async () => {
  // 可以在这里获取其他需要的字典数据
}

setOptions()

// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteDubbingFunc(row)
  })
}

// 多选删除
const onDelete = async () => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.id)
      })
    const res = await deleteDubbingByIds({ ids: IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateDubbingFunc = async (row) => {
  try {
    const res = await findDubbing({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
      formData.value = res.data.redubbing
      dialogFormVisible.value = true
    } else {
      ElMessage.error('获取配音信息失败：' + (res.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取配音信息失败:', error)
    ElMessage.error('获取配音信息失败：' + (error.message || '网络错误'))
  }
}

// 删除行
const deleteDubbingFunc = async (row) => {
  const res = await deleteDubbing({ id: row.id })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    id: undefined,
    script_id: undefined,
    line_id: undefined,
    topic_id: undefined,
    user_id: undefined,
    character_id: undefined,
    character_asset_id: undefined,
    original_url: '',
    original_duration: undefined,
    dubbed_url: '',
    dubbed_duration: undefined,
    is_author: false,
    is_top: false,
    likes: undefined,
    review_status: undefined,
    status: undefined,
    created_at: undefined,
    updated_at: undefined,
  }
}

// 弹窗确定
const enterDialog = async () => {
  elFormRef.value?.validate(async (valid) => {
    if (!valid) return
    btnLoading.value = true
    let res
    switch (type.value) {
      case 'create':
        res = await createDubbing(formData.value)
        break
      case 'update':
        res = await updateDubbing(formData.value)
        break
      default:
        res = await createDubbing(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getTableData()
    }
    btnLoading.value = false
  })
}

// 查看详情
const getDetails = async (row) => {
  try {
    const res = await findDubbing({ id: row.id })
    if (res.code === 0) {
      formData.value = res.data.redubbing
      type.value = 'detail'
      dialogFormVisible.value = true
    } else {
      ElMessage.error('获取配音详情失败：' + (res.msg || '未知错误'))
    }
  } catch (error) {
    console.error('获取配音详情失败:', error)
    ElMessage.error('获取配音详情失败：' + (error.message || '网络错误'))
  }
}

// 审核状态变更处理
const handleReviewStatusChange = async (row, newStatus) => {
  try {
    const res = await auditDubbing({
      id: row.id,
      review_status: newStatus,
      reject_reason: ''
    })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '审核状态更新成功'
      })
      getTableData()
    }
  } catch (error) {
    ElMessage({
      type: 'error',
      message: '审核状态更新失败'
    })
    // 恢复原状态
    getTableData()
  }
}

// 点赞数变更处理
const handleLikesChange = async (row, newLikes) => {
  try {
    const res = await updateDubbing({
      id: row.id,
      likes: newLikes
    })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '点赞数更新成功'
      })
      // 更新本地数据
      row.likes = newLikes
    } else {
      ElMessage({
        type: 'error',
        message: '点赞数更新失败'
      })
      // 恢复原值
      getTableData()
    }
  } catch (error) {
    ElMessage({
      type: 'error',
      message: '点赞数更新失败'
    })
    // 恢复原值
    getTableData()
  }
}

// 置顶状态变更处理
const handleTopChange = async (row, newTopStatus) => {
  try {
    const res = await setDubbingTop({
      id: row.id,
      is_top: newTopStatus
    })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: newTopStatus ? '置顶成功' : '取消置顶成功'
      })
      // 更新本地数据
      row.is_top = newTopStatus
    } else {
      ElMessage({
        type: 'error',
        message: '置顶状态更新失败'
      })
      // 恢复原值
      row.is_top = !newTopStatus
    }
  } catch (error) {
    ElMessage({
      type: 'error',
      message: '置顶状态更新失败'
    })
    // 恢复原值
    row.is_top = !newTopStatus
  }
}

// 检查是否可以设置置顶
const canSetTop = (row) => {
  // 检查状态：1=正常，2=删除
  if (row.status === 2) {
    return false
  }

  // 检查审核状态：3=机审拒绝，5=人审拒绝
  if (row.review_status === 3 || row.review_status === 5) {
    return false
  }

  return true
}

// 人审通过处理
const handleManualApprove = async (row) => {
  try {
    await ElMessageBox.confirm('确定要人审通过该配音吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await auditDubbing({
      id: row.id,
      review_status: 4, // 人审通过
      reject_reason: ''
    })

    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '人审通过成功'
      })
      getTableData()
    } else {
      ElMessage({
        type: 'error',
        message: '人审通过失败'
      })
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage({
        type: 'error',
        message: '人审通过失败'
      })
    }
  }
}

// 人审拒绝处理
const handleManualReject = async (row) => {
  try {
    await ElMessageBox.confirm('确定要人审拒绝该配音吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await auditDubbing({
      id: row.id,
      review_status: 5, // 人审拒绝
      reject_reason: ''
    })

    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '人审拒绝成功'
      })
      getTableData()
    } else {
      ElMessage({
        type: 'error',
        message: '人审拒绝失败'
      })
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage({
        type: 'error',
        message: '人审拒绝失败'
      })
    }
  }
}
</script>

<style scoped>
/* 配音列表样式优化 */
.gva-table-box :deep(.el-table) {
  font-size: 13px;
}

.gva-table-box :deep(.el-table .cell) {
  padding: 6px 8px;
}

/* 剧本信息样式 */
.script-info {
  line-height: 1.4;
}

.script-id {
  font-weight: 500;
  color: #409EFF;
  margin-bottom: 2px;
}

.script-title {
  font-size: 13px;
  color: #606266;
}

/* 音频播放器样式优化 */
.single-audio-wrapper {
  width: 100%;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.original-audio {
  border-color: #bfdbfe;
  background-color: #f0f9ff;
}

.dubbed-audio {
  border-color: #bbf7d0;
  background-color: #f0fdf4;
}

.single-audio-wrapper :deep(.audio-player) {
  width: 100%;
}

.no-audio-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  min-height: 40px;
}

.no-audio-text {
  color: #C0C4CC;
  font-style: italic;
  font-size: 13px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 点赞数输入框样式 */
.likes-input :deep(.el-input-number) {
  width: 80px;
}

.likes-input :deep(.el-input__inner) {
  text-align: center;
}

/* 表单中的音频播放器样式 */
.form-audio-player {
  margin-top: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.form-audio-player :deep(.audio-player) {
  max-width: 100%;
  width: 100%;
}
</style>
