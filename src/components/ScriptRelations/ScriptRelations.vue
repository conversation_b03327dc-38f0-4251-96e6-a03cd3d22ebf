<template>
  <div class="script-relations">
    <!-- 台词列表弹窗 -->
    <el-dialog v-model="linesDialogVisible" title="台词列表" width="90%" :before-close="closeLinesDialog">
      <div class="lines-container">
        <div class="search-bar" style="margin-bottom: 16px;">
          <el-input
            v-model="linesSearchKeyword"
            placeholder="搜索台词内容"
            style="width: 300px; margin-right: 16px;"
            clearable
            @clear="searchLines"
            @keyup.enter="searchLines"
          />
          <el-button type="primary" @click="searchLines">搜索</el-button>
        </div>

        <el-table :data="linesData" style="width: 100%" v-loading="linesLoading">
          <el-table-column prop="id" label="台词ID" width="100" />
          <el-table-column prop="content" label="台词内容" min-width="200" />
          <el-table-column prop="character_id" label="角色" width="150">
            <template #default="scope">
              <div v-if="scope.row.character_id" class="character-info">
                <div class="character-id">ID: {{ scope.row.character_id }}</div>
                <div class="character-name">{{ charactersCache.get(scope.row.character_id)?.name || `角色${scope.row.character_id}` }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="background_url" label="背景图" width="150">
            <template #default="scope">
              <div v-if="scope.row.background_url" style="display: flex; align-items: center; gap: 8px;">
                <el-image
                  :src="scope.row.background_url"
                  :preview-src-list="[scope.row.background_url]"
                  fit="cover"
                  style="width: 60px; height: 40px; border-radius: 4px;"
                  :preview-teleported="true"
                />
                <div
                  v-if="scope.row.bg_theme_color"
                  :style="{
                    width: '20px',
                    height: '20px',
                    backgroundColor: scope.row.bg_theme_color,
                    borderRadius: '3px',
                    border: '1px solid #ddd',
                    flexShrink: 0
                  }"
                  :title="`主题色: ${scope.row.bg_theme_color}`"
                ></div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="dubbing_count" label="配音数" width="100" />
          <el-table-column prop="sort" label="排序" width="80" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              {{ getLineStatusText(scope.row.status) }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="scope">
              {{ formatTimestamp(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="参考音频预览" width="300">
            <template #default="scope">
              <div v-if="scope.row.dubbed_url" class="dubbing-info">
                 <AudioPlayer :src="scope.row.dubbed_url"/>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="showDubbings(scope.row)">配音</el-button>
              <el-button type="success" link size="small" @click="handleReDubbing(scope.row)" :loading="scope.row.reDubbingLoading">AI重配</el-button>
              <el-button type="warning" link size="small" @click="handleManualDubbing(scope.row)" :loading="scope.row.manualDubbingLoading">手动配音</el-button>
              <el-button type="primary" link size="small" @click="handleEditDubbing(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container" style="margin-top: 16px; text-align: right;">
          <el-pagination
            v-model:current-page="linesPage"
            v-model:page-size="linesPageSize"
            :page-sizes="[20, 50, 100]"
            :total="linesTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleLinesSizeChange"
            @current-change="handleLinesCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 配音列表弹窗 -->
    <el-dialog v-model="dubbingsDialogVisible" title="配音列表" width="90%" :before-close="closeDubbingsDialog">
      <div class="dubbings-container">
        <div class="info-bar" style="margin-bottom: 16px; padding: 12px; background: #f5f7fa; border-radius: 4px;">
          <span style="font-weight: bold;">台词内容：</span>{{ currentLine.content }}
        </div>

        <el-table :data="dubbingsData" style="width: 100%" v-loading="dubbingsLoading">
          <el-table-column prop="id" label="配音ID" width="100" />
          <el-table-column prop="user_id" label="用户ID" width="100" />
          <el-table-column prop="character_id" label="角色" width="150">
            <template #default="scope">
              <div v-if="scope.row.character_id" class="character-info">
                <div class="character-id">ID: {{ scope.row.character_id }}</div>
                <div class="character-name">{{ charactersCache.get(scope.row.character_id)?.name || `角色${scope.row.character_id}` }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="original_url" label="原声配音" width="320">
            <template #default="scope">
              <div v-if="scope.row.original_url" class="single-audio-wrapper original-audio">
                <div class="audio-player-container">
                  <AudioPlayer :src="scope.row.original_url" />
                  <span v-if="scope.row.original_duration && scope.row.original_duration > 0" class="audio-duration">
                    {{ formatDuration(scope.row.original_duration) }}
                  </span>
                </div>
              </div>
              <div v-else class="no-audio-content">
                <span class="no-audio-text">无原声音频</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="dubbed_url" label="变声配音" width="320">
            <template #default="scope">
              <div v-if="scope.row.dubbed_url" class="single-audio-wrapper dubbed-audio">
                <div class="audio-player-container">
                  <AudioPlayer :src="scope.row.dubbed_url" />
                  <span v-if="scope.row.dubbed_duration && scope.row.dubbed_duration > 0" class="audio-duration">
                    {{ formatDuration(scope.row.dubbed_duration) }}
                  </span>
                </div>
              </div>
              <div v-else class="no-audio-content">
                <span class="no-audio-text">无变声音频</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="is_author" label="作者配音" width="100">
            <template #default="scope">
              {{ scope.row.is_author ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="is_top" label="置顶" width="80">
            <template #default="scope">
              {{ scope.row.is_top ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="likes" label="点赞数" width="100" />
          <el-table-column v-if="!isCreator" prop="review_status" label="审核状态" width="160">
            <template #default="scope">
              <el-select
                v-model="scope.row.review_status"
                placeholder="审核状态"
                size="small"
                style="width: 140px;"
                @change="handleDubbingReviewStatusChange(scope.row, $event)"
              >
                <el-option
                  v-for="(item, key) in reviewStatusOptions"
                  :key="key"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="scope">
              {{ formatTimestamp(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="showDubbingComments(scope.row)">评论</el-button>
              <el-button v-if="!isCreator" type="danger" link size="small" @click="deleteDubbingRow(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container" style="margin-top: 16px; text-align: right;">
          <el-pagination
            v-model:current-page="dubbingsPage"
            v-model:page-size="dubbingsPageSize"
            :page-sizes="[20, 50, 100]"
            :total="dubbingsTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleDubbingsSizeChange"
            @current-change="handleDubbingsCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 评论列表弹窗 -->
    <el-dialog v-model="commentsDialogVisible" :title="commentDialogTitle" width="90%" :before-close="closeCommentsDialog">
      <div class="comments-container">
        <div class="info-bar" v-if="currentCommentType === 2" style="margin-bottom: 16px; padding: 12px; background: #f5f7fa; border-radius: 4px;">
          <span style="font-weight: bold;">配音ID：</span>{{ currentDubbing.id }}
        </div>

        <div class="comments-list">
          <div v-for="comment in commentsData" :key="comment.id" class="comment-item">
            <!-- 主评论 -->
            <div class="main-comment">
              <div class="comment-header">
                <span class="comment-id">ID: {{ comment.id }}</span>
                <span class="user-info">用户: {{ comment.user_id }}</span>
                <span class="character-info" v-if="comment.character_name">角色: {{ comment.character_name }}</span>
                <span class="comment-time">{{ formatTimestamp(comment.created_at) }}</span>
              </div>

              <div class="comment-content">
                <div v-if="comment.content_type === 1" class="text-content">{{ comment.content }}</div>
                <div v-else-if="comment.content_type === 2" class="voice-comment">
                  <AudioPlayer v-if="comment.voice_url" :src="comment.voice_url" />
                  <div v-if="comment.asr_text" class="asr-text">转写：{{ comment.asr_text }}</div>
                </div>
              </div>

              <div class="comment-meta">
                <span class="content-type">{{ getCommentContentTypeText(comment.content_type) }}</span>
                <span v-if="comment.is_top" class="tag top-tag">置顶</span>
                <span v-if="comment.is_hot" class="tag hot-tag">热门</span>
                <span v-if="comment.is_author" class="tag author-tag">作者</span>
                <span class="likes">👍 {{ comment.likes || 0 }}</span>
                <div v-if="!isCreator" class="review-status-container">
                  <el-select
                    v-model="comment.review_status"
                    placeholder="审核状态"
                    size="small"
                    style="width: 120px;"
                    @change="handleCommentReviewStatusChange(comment, $event)"
                  >
                    <el-option
                      v-for="(item, key) in reviewStatusOptions"
                      :key="key"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </div>

              <div class="comment-actions">
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="toggleReplies(comment)"
                  v-if="comment.replyCount > 0"
                >
                  {{ comment.showReplies ? '收起回复' : `查看回复(${comment.replyCount})` }}
                </el-button>
                <el-button v-if="!isCreator" type="danger" link size="small" @click="deleteCommentRow(comment)">删除</el-button>
              </div>
            </div>

            <!-- 回复列表 -->
            <div v-if="comment.showReplies && comment.replies.length > 0" class="replies-container">
              <template v-for="reply in comment.replies" :key="reply.id">
                <div class="reply-item" :style="{ marginLeft: '0px' }">
                  <div class="reply-header">
                    <span class="reply-id">ID: {{ reply.id }}</span>
                    <span class="user-info">用户: {{ reply.user_id }}</span>
                    <span class="character-info" v-if="reply.character_name">角色: {{ reply.character_name }}</span>
                    <span class="reply-time">{{ formatTimestamp(reply.created_at) }}</span>
                    <span class="level-indicator">2级回复</span>
                  </div>

                  <div class="reply-content">
                    <div v-if="reply.content_type === 1" class="text-content">{{ reply.content }}</div>
                    <div v-else-if="reply.content_type === 2" class="voice-comment">
                      <AudioPlayer v-if="reply.voice_url" :src="reply.voice_url" />
                      <div v-if="reply.asr_text" class="asr-text">转写：{{ reply.asr_text }}</div>
                    </div>
                  </div>

                  <div class="reply-meta">
                    <span class="content-type">{{ getCommentContentTypeText(reply.content_type) }}</span>
                    <span v-if="reply.is_author" class="tag author-tag">作者</span>
                    <span class="likes">👍 {{ reply.likes || 0 }}</span>
                    <div v-if="!isCreator" class="review-status-container">
                      <el-select
                        v-model="reply.review_status"
                        placeholder="审核状态"
                        size="small"
                        style="width: 120px;"
                        @change="handleCommentReviewStatusChange(reply, $event)"
                      >
                        <el-option
                          v-for="(item, key) in reviewStatusOptions"
                          :key="key"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </div>
                    <el-button v-if="!isCreator" type="danger" link size="small" @click="deleteCommentRow(reply)">删除</el-button>
                  </div>
                </div>

                <!-- 三级及以上回复 -->
                <template v-if="reply.replies && reply.replies.length > 0">
                  <div v-for="subReply in reply.replies" :key="subReply.id" class="reply-item" :style="{ marginLeft: '20px' }">
                    <div class="reply-header">
                      <span class="reply-id">ID: {{ subReply.id }}</span>
                      <span class="user-info">用户: {{ subReply.user_id }}</span>
                      <span class="character-info" v-if="subReply.character_name">角色: {{ subReply.character_name }}</span>
                      <span class="reply-time">{{ formatTimestamp(subReply.created_at) }}</span>
                      <span class="level-indicator">3级回复</span>
                    </div>

                    <div class="reply-content">
                      <div v-if="subReply.content_type === 1" class="text-content">{{ subReply.content }}</div>
                      <div v-else-if="subReply.content_type === 2" class="voice-comment">
                        <AudioPlayer v-if="subReply.voice_url" :src="subReply.voice_url" />
                        <div v-if="subReply.asr_text" class="asr-text">转写：{{ subReply.asr_text }}</div>
                      </div>
                    </div>

                    <div class="reply-meta">
                      <span class="content-type">{{ getCommentContentTypeText(subReply.content_type) }}</span>
                      <span v-if="subReply.is_author" class="tag author-tag">作者</span>
                      <span class="likes">👍 {{ subReply.likes || 0 }}</span>
                      <div v-if="!isCreator" class="review-status-container">
                        <el-select
                          v-model="subReply.review_status"
                          placeholder="审核状态"
                          size="small"
                          style="width: 120px;"
                          @change="handleCommentReviewStatusChange(subReply, $event)"
                        >
                          <el-option
                            v-for="(item, key) in reviewStatusOptions"
                            :key="key"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </div>
                      <el-button v-if="!isCreator" type="danger" link size="small" @click="deleteCommentRow(subReply)">删除</el-button>
                    </div>

                    <!-- 四级及以上回复 -->
                    <template v-if="subReply.replies && subReply.replies.length > 0">
                      <div v-for="subSubReply in subReply.replies" :key="subSubReply.id" class="reply-item" :style="{ marginLeft: '40px' }">
                        <div class="reply-header">
                          <span class="reply-id">ID: {{ subSubReply.id }}</span>
                          <span class="user-info">用户: {{ subSubReply.user_id }}</span>
                          <span class="character-info" v-if="subSubReply.character_name">角色: {{ subSubReply.character_name }}</span>
                          <span class="reply-time">{{ formatTimestamp(subSubReply.created_at) }}</span>
                          <span class="level-indicator">4级回复</span>
                        </div>

                        <div class="reply-content">
                          <div v-if="subSubReply.content_type === 1" class="text-content">{{ subSubReply.content }}</div>
                          <div v-else-if="subSubReply.content_type === 2" class="voice-comment">
                            <AudioPlayer v-if="subSubReply.voice_url" :src="subSubReply.voice_url" />
                            <div v-if="subSubReply.asr_text" class="asr-text">转写：{{ subSubReply.asr_text }}</div>
                          </div>
                        </div>

                        <div class="reply-meta">
                          <span class="content-type">{{ getCommentContentTypeText(subSubReply.content_type) }}</span>
                          <span v-if="subSubReply.is_author" class="tag author-tag">作者</span>
                          <span class="likes">👍 {{ subSubReply.likes || 0 }}</span>
                          <div v-if="!isCreator" class="review-status-container">
                            <el-select
                              v-model="subSubReply.review_status"
                              placeholder="审核状态"
                              size="small"
                              style="width: 120px;"
                              @change="handleCommentReviewStatusChange(subSubReply, $event)"
                            >
                              <el-option
                                v-for="(item, key) in reviewStatusOptions"
                                :key="key"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                          </div>
                          <el-button v-if="!isCreator" type="danger" link size="small" @click="deleteCommentRow(subSubReply)">删除</el-button>
                        </div>
                      </div>
                    </template>
                  </div>
                </template>
              </template>
            </div>
          </div>
        </div>

        <div class="pagination-container" style="margin-top: 16px; text-align: right;">
          <el-pagination
            v-model:current-page="commentsPage"
            v-model:page-size="commentsPageSize"
            :page-sizes="[20, 50, 100]"
            :total="commentsTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleCommentsSizeChange"
            @current-change="handleCommentsCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 角色列表弹窗 -->
    <el-dialog v-model="charactersDialogVisible" title="角色列表" width="80%" :before-close="closeCharactersDialog">
      <div class="characters-container">
        <el-table :data="charactersData" style="width: 100%" v-loading="charactersLoading">
          <el-table-column prop="id" label="关联ID" width="100" />
          <el-table-column prop="character_id" label="角色ID" width="100" />
          <el-table-column prop="character_name" label="角色名称" min-width="150" />
          <el-table-column prop="character_asset_id" label="角色资源ID" width="120" />
           <el-table-column prop="asset_url" label="角色图片" width="120">
            <template #default="scope">
              <el-image
                v-if="scope.row.asset_url"
                :src="scope.row.asset_url"
                :preview-src-list="[scope.row.asset_url]"
                fit="cover"
                style="width: 60px; height: 40px; border-radius: 4px;"
                :preview-teleported="true"
              />
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="line_count" label="台词数" width="100" />
          <el-table-column prop="sort" label="排序" width="80" />
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="scope">
              {{ formatTimestamp(scope.row.created_at) }}
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container" style="margin-top: 16px; text-align: right;">
          <el-pagination
            v-model:current-page="charactersPage"
            v-model:page-size="charactersPageSize"
            :page-sizes="[20, 50, 100]"
            :total="charactersTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleCharactersSizeChange"
            @current-change="handleCharactersCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 话题列表弹窗 -->
    <el-dialog v-model="topicsDialogVisible" title="话题列表" width="80%" :before-close="closeTopicsDialog">
      <div class="topics-container">
        <el-table :data="topicsData" style="width: 100%" v-loading="topicsLoading">
          <el-table-column prop="id" label="关联ID" width="100" />
          <el-table-column prop="topic_id" label="话题ID" width="100" />
          <el-table-column prop="topic_name" label="话题名称" min-width="200" />
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="scope">
              {{ formatTimestamp(scope.row.created_at) }}
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container" style="margin-top: 16px; text-align: right;">
          <el-pagination
            v-model:current-page="topicsPage"
            v-model:page-size="topicsPageSize"
            :page-sizes="[20, 50, 100]"
            :total="topicsTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleTopicsSizeChange"
            @current-change="handleTopicsCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

  <!-- 编辑台词弹窗 -->
  <el-dialog v-model="editLineDialogVisible" title="编辑台词" width="60%" :before-close="closeEditLineDialog">
    <div class="edit-line-container">
      <el-form :model="editLineForm" label-width="100px">
        <el-form-item label="台词ID">
        <el-input
          v-model="editLineForm.id"
          disabled
          placeholder="台词ID"
        />
        </el-form-item>
        <el-form-item label="台词内容">
          <el-input
            v-model="editLineForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入台词内容"
          />
        </el-form-item>
        <el-form-item label="背景图">
          <div style="display: flex; flex-direction: column; gap: 12px;">
            <div style="display: flex; gap: 12px; align-items: center;">
              <el-input
                v-model="editLineForm.background_url"
                placeholder="请输入背景图URL或点击选择/上传"
                style="flex: 1;"
                clearable
              />
              <el-button type="primary" @click="openBackgroundImageSelector">
                选择图片
              </el-button>
              <el-upload
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleBackgroundImageUpload"
                accept="image/*"
              >
                <el-button type="success" :loading="backgroundImageUploading">
                  {{ backgroundImageUploading ? '上传中...' : '上传图片' }}
                </el-button>
              </el-upload>
              <el-button
                v-if="editLineForm.background_url"
                type="danger"
                @click="clearBackgroundImage"
              >
                清除
              </el-button>
            </div>
            <div v-if="editLineForm.background_url" style="display: flex; align-items: center; gap: 12px;">
              <el-image
                :src="editLineForm.background_url"
                :preview-src-list="[editLineForm.background_url]"
                fit="cover"
                style="width: 120px; height: 80px; border-radius: 4px; border: 1px solid #ddd; cursor: pointer;"
                :preview-teleported="true"
              />
              <div v-if="editLineForm.bg_theme_color" style="display: flex; flex-direction: column; align-items: center; gap: 4px;">
                <div
                  :style="{
                    width: '40px',
                    height: '40px',
                    backgroundColor: editLineForm.bg_theme_color,
                    borderRadius: '4px',
                    border: '1px solid #ddd',
                    cursor: 'pointer'
                  }"
                  :title="`主题色: ${editLineForm.bg_theme_color}`"
                ></div>
                <span style="font-size: 12px; color: #666;">{{ editLineForm.bg_theme_color }}</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeEditLineDialog">取消</el-button>
        <el-button type="primary" @click="submitEditLine" :loading="editLineLoading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 背景图选择弹窗 -->
  <el-dialog v-model="backgroundImageSelectorVisible" title="选择背景图" width="80%" :before-close="closeBackgroundImageSelector">
    <div class="background-image-selector">
      <div class="selector-toolbar" style="margin-bottom: 16px;">
        <el-input
          v-model="backgroundImageSearch"
          placeholder="搜索图片..."
          style="width: 300px; margin-right: 12px;"
          clearable
          @input="searchBackgroundImages"
        />
        <el-button type="primary" @click="refreshBackgroundImages">刷新</el-button>
      </div>
      <div class="image-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 12px; max-height: 400px; overflow-y: auto;">
        <div
          v-for="image in filteredBackgroundImages"
          :key="image.id || image.url"
          class="image-item"
          style="border: 2px solid transparent; border-radius: 8px; cursor: pointer; transition: all 0.2s;"
          :style="{ borderColor: selectedBackgroundImage?.url === image.url ? '#409EFF' : 'transparent' }"
          @click="selectBackgroundImage(image)"
        >
          <el-image
            :src="image.url"
            fit="cover"
            style="width: 100%; height: 100px; border-radius: 6px;"
            :preview-src-list="[image.url]"
            :preview-teleported="true"
          />
          <div style="padding: 8px; text-align: center; font-size: 12px; color: #666;">
            {{ image.name || '未命名' }}
          </div>
        </div>
      </div>
      <div v-if="filteredBackgroundImages.length === 0" style="text-align: center; padding: 40px; color: #999;">
        暂无图片数据
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeBackgroundImageSelector">取消</el-button>
        <el-button type="primary" @click="confirmBackgroundImageSelection" :disabled="!selectedBackgroundImage">
          确定选择
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 手动配音弹窗 -->
  <el-dialog v-model="manualDubbingDialogVisible" title="手动上传配音" width="700px" :before-close="closeManualDubbingDialog">
    <div class="manual-dubbing-container">
      <div class="info-bar" style="margin-bottom: 16px; padding: 12px; background: #f5f7fa; border-radius: 4px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div style="flex: 1;">
            <div style="margin-bottom: 8px;">
              <span style="font-weight: bold;">台词内容：</span>{{ currentManualDubbingLine.content }}
            </div>
            <div v-if="currentManualDubbingLine.character_id" class="character-info-display" style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold; font-size: 13px; color: #666;">绑定角色：</span>
              <el-avatar
                v-if="charactersCache.get(currentManualDubbingLine.character_id)?.avatarUrl"
                :src="charactersCache.get(currentManualDubbingLine.character_id)?.avatarUrl"
                :size="36"
                style="flex-shrink: 0; border: 2px solid #e9ecef;"
              />
              <span style="font-size: 14px; color: #495057; font-weight: 500;">
                {{ charactersCache.get(currentManualDubbingLine.character_id)?.name || `角色${currentManualDubbingLine.character_id}` }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <el-form :model="manualDubbingForm" label-width="100px">
        <el-form-item label="配音文件" required>
          <div class="audio-upload-container">
            <!-- 上传按钮区域 -->
            <div class="upload-button-area" style="margin-bottom: 12px;">
              <el-upload
                class="audio-uploader"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleManualDubbingFileChange"
                accept="audio/*"
              >
                <el-button type="primary" :loading="manualDubbingUploading">
                  {{ manualDubbingForm.original_url ? '重新上传' : '上传音频' }}
                </el-button>
              </el-upload>
            </div>

            <!-- 原声音频播放器区域 -->
            <div v-if="manualDubbingForm.original_url" class="audio-player-area" style="margin-bottom: 16px;">
              <div
                class="audio-preview-container clickable-audio-container"
                @click="selectAudioType('original')"
              >
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                  <el-radio
                    v-model="manualDubbingForm.selected_audio_type"
                    value="original"
                    style="margin: 0; pointer-events: none;"
                  />
                  <div class="audio-preview-label" style="font-weight: bold;">原声音频</div>
                </div>
                <div @click.stop>
                  <AudioPlayer :src="manualDubbingForm.original_url" />
                </div>
              </div>
            </div>

            <!-- 变声按钮区域 -->
            <div v-if="manualDubbingForm.original_url && currentManualDubbingLine.character_id && currentManualDubbingLine.character_asset_id" class="voice-change-area" style="margin-bottom: 16px;">
              <el-button
                type="success"
                :loading="voiceChangeLoading"
                @click="handleVoiceChangeForDubbing"
              >
                变声
              </el-button>
              <el-text type="info" size="small" style="margin-left: 12px;">
                使用当前台词绑定的角色资源包进行变声处理
              </el-text>
            </div>

            <!-- 变声不可用提示 -->
            <div v-if="manualDubbingForm.original_url && currentManualDubbingLine.character_id && !currentManualDubbingLine.character_asset_id" class="voice-change-unavailable" style="margin-bottom: 16px;">
              <div style="padding: 12px; background: #fef3cd; border-radius: 6px; border: 1px solid #ffeaa7;">
                <el-text type="warning" size="small">
                  该台词未绑定角色资源包，无法使用变声功能。请在剧本管理中为台词绑定角色资源包。
                </el-text>
              </div>
            </div>

            <!-- 变声音频播放器区域 -->
            <div v-if="manualDubbingForm.voice_changed_url" class="voice-changed-audio-area" style="margin-bottom: 16px;">
              <div
                class="audio-preview-container clickable-audio-container"
                @click="selectAudioType('voice_changed')"
              >
                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                  <el-radio
                    v-model="manualDubbingForm.selected_audio_type"
                    value="voice_changed"
                    style="margin: 0; pointer-events: none;"
                  />
                  <div class="audio-preview-label" style="font-weight: bold;">变声音频</div>
                </div>
                <div @click.stop>
                  <AudioPlayer :src="manualDubbingForm.voice_changed_url" />
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeManualDubbingDialog">取消</el-button>
        <el-button type="primary" @click="submitManualDubbing" :loading="manualDubbingSubmitting">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
  </div>
</template>

<style scoped>
.manual-dubbing-container {
  .audio-upload-container {
    .audio-preview-container {
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;
      transition: all 0.2s ease;

      &:has(input[type="radio"]:checked) {
        background: #e3f2fd;
        border-color: #2196f3;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
      }

      &.clickable-audio-container {
        cursor: pointer;
        user-select: none;

        &:hover {
          background: #f0f0f0;
          border-color: #d0d0d0;
        }

        &:active {
          transform: translateY(1px);
        }

        &:has(input[type="radio"]:checked) {
          &:hover {
            background: #d1e7fd;
            border-color: #1976d2;
          }
        }
      }
    }

    .audio-preview-label {
      color: #495057;
      font-size: 14px;
    }
  }

  .character-info-display {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 4px 0;

    .el-avatar {
      border: 2px solid #e9ecef;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }

      /* 确保头像图片清晰显示 */
      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }

    /* 角色名称样式优化 */
    span:last-child {
      line-height: 1.4;
      word-break: break-all;
      max-width: 200px;
    }
  }

  .voice-change-area {
    padding: 12px;
    background: #f0f9ff;
    border-radius: 6px;
    border: 1px solid #bfdbfe;
  }

  .voice-changed-audio-area {
    .audio-preview-container {
      padding: 12px;
      background: #f0fdf4;
      border-radius: 6px;
      border: 1px solid #bbf7d0;
      transition: all 0.2s ease;

      &:has(input[type="radio"]:checked) {
        background: #e8f5e8;
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
      }

      &.clickable-audio-container {
        cursor: pointer;
        user-select: none;

        &:hover {
          background: #ecfdf5;
          border-color: #a7f3d0;
        }

        &:active {
          transform: translateY(1px);
        }

        &:has(input[type="radio"]:checked) {
          &:hover {
            background: #dcfce7;
            border-color: #22c55e;
          }
        }
      }
    }
  }

  .audio-player-area {
    .audio-preview-container {
      &:has(input[type="radio"]:checked) {
        background: #e3f2fd;
        border-color: #2196f3;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
      }
    }
  }
}

.character-info {
  .character-id {
    font-size: 12px;
    color: #666;
  }

  .character-name {
    font-size: 13px;
    color: #333;
    font-weight: 500;
  }
}

/* 单选框样式优化 */
.manual-dubbing-container .el-radio {
  --el-radio-font-size: 14px;

  .el-radio__input.is-checked .el-radio__inner {
    background-color: #409eff;
    border-color: #409eff;
  }

  .el-radio__input.is-checked + .el-radio__label {
    color: #409eff;
    font-weight: 500;
  }
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media (max-width: 768px) {
  .manual-dubbing-container {
    .character-info-display {
      gap: 8px;

      .el-avatar {
        /* 在小屏幕上稍微减小头像尺寸 */
        width: 32px !important;
        height: 32px !important;
      }

      span:last-child {
        font-size: 13px;
        max-width: 150px;
      }
    }
  }
}

@media (min-width: 1200px) {
  .manual-dubbing-container {
    .character-info-display {
      gap: 12px;

      .el-avatar {
        /* 在大屏幕上可以使用更大的头像 */
        width: 40px !important;
        height: 40px !important;
      }

      span:last-child {
        font-size: 15px;
        max-width: 250px;
      }
    }
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .manual-dubbing-container .character-info-display .el-avatar {
    /* 在高分辨率屏幕上确保头像清晰 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
</style>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox, ElAvatar } from 'element-plus'
import AudioPlayer from '@/components/AudioPlayer/AudioPlayer.vue'
import { formatTimestamp, extractThemeColorFromImage } from '@/utils/format'
import { uploadToOSS, checkFileSize } from '@/utils/oss'
import { useUserStore } from '@/pinia'
import {
  getLineStatusText,
  getCommentContentTypeText,
  reviewStatusOptions
} from '@/constants/scriptStatus'

// 导入API
import { getLineList, reDubbing, updateLine } from '@/api/scripts/lines'
import { getDubbingList, deleteDubbing, updateDubbing, manualUploadDubbing } from '@/api/scripts/dubbings'
import { getCommentList, deleteComment, updateComment } from '@/api/scripts/comments'
import { getScriptCharacterRelationList } from '@/api/scripts/scriptCharacterRelations'
import { getScriptTopicRelationList } from '@/api/scripts/scriptTopicRelations'
import { findCharacter } from '@/api/characters/characters'
import { getCharacterAssetsList } from '@/api/characters/characterAssets'
import { getFileList } from '@/api/fileUploadAndDownload'
import { getPreset } from '@/api/characters/characterAssets'
import { loadModel, inferRvc } from '@/api/rvc/rvc'
import { v4 as uuidv4 } from 'uuid'

const props = defineProps({
  script: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['refresh'])

// 获取用户store
const userStore = useUserStore()

// 计算当前用户是否为角色111（创作者）
const isCreator = computed(() => {
  return userStore.userInfo?.authority?.authorityId === 111
})

// 台词相关状态
const linesDialogVisible = ref(false)
const linesData = ref([])
const linesLoading = ref(false)
const linesPage = ref(1)
const linesPageSize = ref(20)
const linesTotal = ref(0)
const linesSearchKeyword = ref('')

// 配音相关状态
const dubbingsDialogVisible = ref(false)
const dubbingsData = ref([])
const dubbingsLoading = ref(false)
const dubbingsPage = ref(1)
const dubbingsPageSize = ref(20)
const dubbingsTotal = ref(0)
const currentLine = ref({})

// 评论相关状态
const commentsDialogVisible = ref(false)
const commentsData = ref([])
const commentsLoading = ref(false)
const commentsPage = ref(1)
const commentsPageSize = ref(20)
const commentsTotal = ref(0)
const currentCommentType = ref(1) // 1:剧本评论 2:配音评论
const currentDubbing = ref({})
const commentDialogTitle = ref('评论列表')

// 角色相关状态
const charactersDialogVisible = ref(false)
const charactersData = ref([])
const charactersLoading = ref(false)
const charactersPage = ref(1)
const charactersPageSize = ref(20)
const charactersTotal = ref(0)

// 话题相关状态
const topicsDialogVisible = ref(false)
const topicsData = ref([])
const topicsLoading = ref(false)
const topicsPage = ref(1)
const topicsPageSize = ref(10)
const topicsTotal = ref(0)

// 当前操作的剧本ID
const currentScriptId = ref(null)

// 角色数据缓存 - 存储角色的详细信息
const charactersCache = ref(new Map()) // key: characterId, value: { name, avatarUrl }

// 格式化时长显示
const formatDuration = (duration) => {
  if (!duration || duration <= 0) {
    return '0.0s'
  }

  // duration是毫秒，转换为秒并保留1位小数
  const seconds = duration / 1000
  return `${seconds.toFixed(1)}s`
}

// 手动配音相关状态
const manualDubbingDialogVisible = ref(false)
const currentManualDubbingLine = ref({})
const manualDubbingForm = ref({
  original_url: '',           // 原声音频URL
  voice_changed_url: '',      // 变声音频URL
  dubbed_url: '',             // 最终提交的音频URL
  character_id: null,
  dubbed_duration: 0,
  selected_audio_type: 'original'  // 选择的音频类型：'original' 或 'voice_changed'
})
const manualDubbingUploading = ref(false)
const manualDubbingSubmitting = ref(false)
const voiceChangeLoading = ref(false)  // 变声处理loading状态

// 批量获取角色详细信息（包括头像）
const batchGetCharacterInfo = async (characterIds) => {
  const uniqueIds = [...new Set(characterIds.filter(id => id && !charactersCache.value.has(id)))]

  if (uniqueIds.length === 0) return

  try {
    // 使用 findCharacter API 逐个获取角色基本信息
    const characterPromises = uniqueIds.map(async (id) => {
      try {
        const res = await findCharacter({ id: id })
        if (res.code === 0 && res.data) {
          const character = res.data
          return { id, character }
        }
        return { id, character: null }
      } catch (error) {
        console.warn(`获取角色${id}基本信息失败:`, error)
        return { id, character: null }
      }
    })

    const characterResults = await Promise.all(characterPromises)

    // 获取角色资源包信息（包含头像）
    const assetPromises = uniqueIds.map(async (id) => {
      try {
        const res = await getCharacterAssetsList({
          character_id: id,
          page: 1,
          pageSize: 1,
          is_default: true // 获取默认资源包
        })
        if (res.code === 0 && res.data?.list?.length > 0) {
          return { id, asset: res.data.list[0] }
        }
        return { id, asset: null }
      } catch (error) {
        console.warn(`获取角色${id}资源包失败:`, error)
        return { id, asset: null }
      }
    })

    const assetResults = await Promise.all(assetPromises)

    // 合并角色信息和资源包信息
    characterResults.forEach(({ id, character }) => {
      const assetResult = assetResults.find(result => result.id === id)
      const asset = assetResult?.asset

      const characterInfo = {
        name: character?.name || `角色${id}`,
        avatarUrl: asset?.avatar_url || ''
      }

      charactersCache.value.set(id, characterInfo)
    })

  } catch (error) {
    console.warn('批量获取角色信息失败:', error)
    // 为所有ID设置默认信息
    uniqueIds.forEach(id => {
      charactersCache.value.set(id, {
        name: `角色${id}`,
        avatarUrl: ''
      })
    })
  }
}

// 兼容性函数 - 保持向后兼容
const batchGetCharacterNames = batchGetCharacterInfo

// 显示台词列表
const showLines = async (scriptId = null) => {
  if (scriptId) {
    currentScriptId.value = scriptId
  } else if (props.script?.id) {
    currentScriptId.value = props.script.id
  }

  linesDialogVisible.value = true
  await getLines()
}

// 获取台词列表
const getLines = async () => {
  try {
    linesLoading.value = true
    const scriptId = currentScriptId.value || props.script?.id

    if (!scriptId) {
      ElMessage.error('剧本ID不能为空')
      return
    }

    const params = {
      script_id: scriptId,
      page: linesPage.value,
      pageSize: linesPageSize.value,
      keyword: linesSearchKeyword.value
    }



    const res = await getLineList(params)
    if (res.code === 0) {
      linesData.value = res.data.list || []
      linesTotal.value = res.data.total || 0
      linesPage.value = res.data.page || 1
      linesPageSize.value = res.data.pageSize || 20

      // 批量获取角色名称
      const characterIds = linesData.value.map(line => line.character_id).filter(id => id)
      if (characterIds.length > 0) {
        await batchGetCharacterNames(characterIds)
      }

      // 为每个台词初始化loading状态
      linesData.value.forEach(line => {
        line.reDubbingLoading = false
        line.manualDubbingLoading = false
      })
    }
  } catch (error) {
    ElMessage.error('获取台词列表失败')
  } finally {
    linesLoading.value = false
  }
}

// 搜索台词
const searchLines = () => {
  linesPage.value = 1
  getLines()
}

// 台词分页处理
const handleLinesSizeChange = (val) => {
  linesPageSize.value = val
  getLines()
}

const handleLinesCurrentChange = (val) => {
  linesPage.value = val
  getLines()
}

// 关闭台词弹窗
const closeLinesDialog = () => {
  linesDialogVisible.value = false
  linesData.value = []
  linesSearchKeyword.value = ''
  linesPage.value = 1
}

// 处理AI重配
const handleReDubbing = async (line) => {
  try {
    console.log('=== 开始AI重配 ===', { lineId: line.id, content: line.content })

    // 设置loading状态
    line.reDubbingLoading = true

    // 调用AI重配接口
    const res = await reDubbing({ id: line.id })

    if (res.code === 0) {
      ElMessage.success('AI重配请求已提交，请稍后查看配音列表')
      // 刷新台词列表以获取最新数据
      await getLines()
      // 通知父组件刷新数据
      emit('refresh')
    } else {
      ElMessage.error(res.msg || 'AI重配失败')
    }
  } catch (error) {
    console.error('AI重配失败:', error)
    ElMessage.error('AI重配失败，请稍后重试')
  } finally {
    // 重置loading状态
    line.reDubbingLoading = false
  }
}

// 显示配音列表
const showDubbings = async (line) => {
  currentLine.value = line
  dubbingsDialogVisible.value = true
  await getDubbings()
}

// 获取配音列表
const getDubbings = async () => {
  try {
    dubbingsLoading.value = true
    const scriptId = currentScriptId.value || props.script?.id

    if (!scriptId) {
      ElMessage.error('剧本ID不能为空')
      return
    }

    const params = {
      script_id: scriptId,
      line_id: currentLine.value.id,
      page: dubbingsPage.value,
      pageSize: dubbingsPageSize.value
    }



    const res = await getDubbingList(params)
    if (res.code === 0) {
      dubbingsData.value = res.data.list || []
      dubbingsTotal.value = res.data.total || 0
      dubbingsPage.value = res.data.page || 1
      dubbingsPageSize.value = res.data.pageSize || 20

      // 批量获取角色名称
      const characterIds = dubbingsData.value.map(dubbing => dubbing.character_id).filter(id => id)
      if (characterIds.length > 0) {
        await batchGetCharacterNames(characterIds)
      }
    }
  } catch (error) {
    ElMessage.error('获取配音列表失败')
  } finally {
    dubbingsLoading.value = false
  }
}

// 配音分页处理
const handleDubbingsSizeChange = (val) => {
  dubbingsPageSize.value = val
  getDubbings()
}

const handleDubbingsCurrentChange = (val) => {
  dubbingsPage.value = val
  getDubbings()
}

// 关闭配音弹窗
const closeDubbingsDialog = () => {
  dubbingsDialogVisible.value = false
  dubbingsData.value = []
  dubbingsPage.value = 1
  currentLine.value = {}
}

// 删除配音
const deleteDubbingRow = (row) => {
  ElMessageBox.confirm('确定要删除这个配音吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteDubbing({ id: row.id })
      if (res.code === 0) {
        ElMessage.success('删除成功')
        getDubbings()
        emit('refresh')
      }
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 处理配音审核状态变更
const handleDubbingReviewStatusChange = async (row, newStatus) => {
  try {
    console.log('=== 配音审核状态变更 ===', { id: row.id, oldStatus: row.review_status, newStatus })

    // 确保状态为数字类型
    const reviewStatus = Number(newStatus)

    const submitData = {
      id: row.id,
      review_status: reviewStatus
    }

    const res = await updateDubbing(submitData)
    if (res.code === 0) {
      ElMessage.success('配音审核状态更新成功')
      // 更新本地数据
      row.review_status = reviewStatus
      // 刷新列表以获取最新数据
      getDubbings()
      emit('refresh')
    } else {
      ElMessage.error('配音审核状态更新失败')
      // 恢复原状态
      getDubbings()
    }
  } catch (error) {
    console.error('配音审核状态更新失败:', error)
    ElMessage.error('配音审核状态更新失败')
    // 恢复原状态
    getDubbings()
  }
}

// 显示配音评论
const showDubbingComments = async (dubbing) => {
  currentDubbing.value = dubbing
  currentCommentType.value = 2
  commentDialogTitle.value = '配音评论'
  commentsDialogVisible.value = true
  await getComments()
}

// 显示剧本评论
const showScriptComments = async (scriptId = null) => {
  if (scriptId) {
    currentScriptId.value = scriptId
  } else if (props.script?.id) {
    currentScriptId.value = props.script.id
  }

  currentCommentType.value = 1
  commentDialogTitle.value = '剧本评论'
  commentsDialogVisible.value = true
  await getComments()
}

// 获取评论列表
const getComments = async () => {
  try {
    commentsLoading.value = true
    const scriptId = currentScriptId.value || props.script?.id

    if (!scriptId) {
      ElMessage.error('剧本ID不能为空')
      return
    }

    const params = {
      comment_type: currentCommentType.value,
      script_id: scriptId,
      page: commentsPage.value,
      pageSize: commentsPageSize.value,
      parent_id: 0 // 只获取一级评论
    }

    if (currentCommentType.value === 2 && currentDubbing.value.id) {
      params.dubbing_id = currentDubbing.value.id
    }

    console.log('=== 获取评论列表参数 ===', params)

    const res = await getCommentList(params)
    if (res.code === 0) {
      const comments = res.data.list || []
      // 为每个评论获取回复数量
      for (const comment of comments) {
        comment.replyCount = await getReplyCount(comment.id)
        comment.showReplies = false
        comment.replies = []
      }
      commentsData.value = comments
      commentsTotal.value = res.data.total || 0
      commentsPage.value = res.data.page || 1
      commentsPageSize.value = res.data.pageSize || 20
    }
  } catch (error) {
    ElMessage.error('获取评论列表失败')
  } finally {
    commentsLoading.value = false
  }
}

// 获取回复数量
const getReplyCount = async (parentId) => {
  try {
    const scriptId = currentScriptId.value || props.script?.id

    if (!scriptId) {
      return 0
    }

    const params = {
      comment_type: currentCommentType.value,
      script_id: scriptId,
      parent_id: parentId,
      page: 1,
      pageSize: 1
    }

    if (currentCommentType.value === 2 && currentDubbing.value.id) {
      params.dubbing_id = currentDubbing.value.id
    }

    const res = await getCommentList(params)
    return res.data?.total || 0
  } catch (error) {
    return 0
  }
}

// 切换回复显示
const toggleReplies = async (comment) => {
  if (comment.showReplies) {
    comment.showReplies = false
    comment.replies = []
  } else {
    await loadReplies(comment)
    comment.showReplies = true
  }
}

// 递归加载所有层级的回复
const loadReplies = async (comment) => {
  try {
    const scriptId = currentScriptId.value || props.script?.id

    if (!scriptId) {
      ElMessage.error('剧本ID不能为空')
      return
    }

    // 递归获取所有回复
    const allReplies = await getAllRepliesRecursively(comment.id, scriptId)
    comment.replies = allReplies
  } catch (error) {
    ElMessage.error('获取回复失败')
  }
}

// 递归获取所有回复的辅助函数
const getAllRepliesRecursively = async (parentId, scriptId) => {
  try {
    const params = {
      comment_type: currentCommentType.value,
      script_id: scriptId,
      parent_id: parentId,
      page: 1,
      pageSize: 100 // 一次性加载所有回复
    }

    if (currentCommentType.value === 2 && currentDubbing.value.id) {
      params.dubbing_id = currentDubbing.value.id
    }

    const res = await getCommentList(params)
    if (res.code === 0) {
      const replies = res.data.list || []

      // 为每个回复递归获取其子回复
      for (const reply of replies) {
        const subReplies = await getAllRepliesRecursively(reply.id, scriptId)
        if (subReplies.length > 0) {
          reply.replies = subReplies
          reply.hasReplies = true
        } else {
          reply.replies = []
          reply.hasReplies = false
        }
      }

      return replies
    }
    return []
  } catch (error) {
    console.error('递归获取回复失败:', error)
    return []
  }
}

// 评论分页处理
const handleCommentsSizeChange = (val) => {
  commentsPageSize.value = val
  getComments()
}

const handleCommentsCurrentChange = (val) => {
  commentsPage.value = val
  getComments()
}

// 关闭评论弹窗
const closeCommentsDialog = () => {
  commentsDialogVisible.value = false
  commentsData.value = []
  commentsPage.value = 1
  currentDubbing.value = {}
}

// 删除评论
const deleteCommentRow = (row) => {
  ElMessageBox.confirm('确定要删除这个评论吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteComment({ id: row.id })
      if (res.code === 0) {
        ElMessage.success('删除成功')
        getComments()
        emit('refresh')
      }
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 处理评论审核状态变更
const handleCommentReviewStatusChange = async (row, newStatus) => {
  try {
    console.log('=== 评论审核状态变更 ===', { id: row.id, oldStatus: row.review_status, newStatus })

    // 确保状态为数字类型
    const reviewStatus = Number(newStatus)

    const submitData = {
      id: row.id,
      review_status: reviewStatus
    }

    const res = await updateComment(submitData)
    if (res.code === 0) {
      ElMessage.success('评论审核状态更新成功')
      // 更新本地数据
      row.review_status = reviewStatus
      // 刷新列表以获取最新数据
      getComments()
      emit('refresh')
    } else {
      ElMessage.error('评论审核状态更新失败')
      // 恢复原状态
      getComments()
    }
  } catch (error) {
    console.error('评论审核状态更新失败:', error)
    ElMessage.error('评论审核状态更新失败')
    // 恢复原状态
    getComments()
  }
}

// 获取角色列表
const getCharacters = async () => {
  try {
    charactersLoading.value = true
    const scriptId = currentScriptId.value || props.script?.id

    if (!scriptId) {
      ElMessage.error('剧本ID不能为空')
      return
    }

    const params = {
      script_id: scriptId,
      page: charactersPage.value,
      pageSize: charactersPageSize.value
    }

    console.log('=== 获取角色列表参数 ===', params)

    const res = await getScriptCharacterRelationList(params)
    if (res.code === 0) {
      charactersData.value = res.data.list || []
      charactersTotal.value = res.data.total || 0
      charactersPage.value = res.data.page || 1
      charactersPageSize.value = res.data.pageSize || 20
    }
  } catch (error) {
    ElMessage.error('获取角色列表失败')
  } finally {
    charactersLoading.value = false
  }
}

// 角色分页处理
const handleCharactersSizeChange = (val) => {
  charactersPageSize.value = val
  getCharacters()
}

const handleCharactersCurrentChange = (val) => {
  charactersPage.value = val
  getCharacters()
}

// 关闭角色弹窗
const closeCharactersDialog = () => {
  charactersDialogVisible.value = false
  charactersData.value = []
  charactersPage.value = 1
}

// 获取话题列表
const getTopics = async () => {
  try {
    topicsLoading.value = true
    const scriptId = currentScriptId.value || props.script?.id

    if (!scriptId) {
      ElMessage.error('剧本ID不能为空')
      return
    }

    const params = {
      script_id: scriptId,
      page: topicsPage.value,
      pageSize: topicsPageSize.value
    }

    console.log('=== 获取话题列表参数 ===', params)

    const res = await getScriptTopicRelationList(params)
    if (res.code === 0) {
      topicsData.value = res.data.list || []
      topicsTotal.value = res.data.total || 0
      topicsPage.value = res.data.page || 1
      topicsPageSize.value = res.data.pageSize || 20
    }
  } catch (error) {
    ElMessage.error('获取话题列表失败')
  } finally {
    topicsLoading.value = false
  }
}

// 话题分页处理
const handleTopicsSizeChange = (val) => {
  topicsPageSize.value = val
  getTopics()
}

const handleTopicsCurrentChange = (val) => {
  topicsPage.value = val
  getTopics()
}

// 关闭话题弹窗
const closeTopicsDialog = () => {
  topicsDialogVisible.value = false
  topicsData.value = []
  topicsPage.value = 1
}

// 暴露方法给父组件
defineExpose({
  showLines,
  showScriptComments,
  showCharacters: (scriptId = null) => {
    if (scriptId) {
      currentScriptId.value = scriptId
    } else if (props.script?.id) {
      currentScriptId.value = props.script.id
    }
    charactersDialogVisible.value = true
    getCharacters()
  },
  showTopics: (scriptId = null) => {
    if (scriptId) {
      currentScriptId.value = scriptId
    } else if (props.script?.id) {
      currentScriptId.value = props.script.id
    }
    topicsDialogVisible.value = true
    getTopics()
  }
})

// 编辑台词相关
// 编辑台词相关状态
const editLineDialogVisible = ref(false)
const editLineForm = ref({
  id: null,
  content: '',
  background_url: '',
  bg_theme_color: ''
})
const editLineLoading = ref(false)

// 背景图选择相关状态
const backgroundImageSelectorVisible = ref(false)
const backgroundImageSearch = ref('')
const backgroundImages = ref([])
const filteredBackgroundImages = ref([])
const selectedBackgroundImage = ref(null)
const backgroundImageUploading = ref(false)

// 处理编辑台词
const handleEditDubbing = (line) => {
  editLineForm.value = {
    id: line.id,
    content: line.content,
    background_url: line.background_url || '',
    bg_theme_color: line.bg_theme_color || ''
  }
  editLineDialogVisible.value = true
}

// 关闭编辑台词弹窗
const closeEditLineDialog = () => {
  editLineDialogVisible.value = false
  editLineForm.value = {
    id: null,
    content: '',
    background_url: '',
    bg_theme_color: ''
  }
}

// 背景图相关方法
// 打开背景图选择器
const openBackgroundImageSelector = async () => {
  await loadBackgroundImages()
  backgroundImageSelectorVisible.value = true
}

// 关闭背景图选择器
const closeBackgroundImageSelector = () => {
  backgroundImageSelectorVisible.value = false
  selectedBackgroundImage.value = null
  backgroundImageSearch.value = ''
}

// 加载背景图列表
const loadBackgroundImages = async () => {
  try {
    // 使用现有的文件列表API获取图片
    const res = await getFileList({
      page: 1,
      pageSize: 100,
      keyword: '',
      classId: 0
    })
    if (res.code === 0) {
      // 过滤出图片文件
      backgroundImages.value = (res.data.list || []).filter(file =>
        file.url && (
          file.url.toLowerCase().includes('.jpg') ||
          file.url.toLowerCase().includes('.jpeg') ||
          file.url.toLowerCase().includes('.png') ||
          file.url.toLowerCase().includes('.gif') ||
          file.url.toLowerCase().includes('.webp')
        )
      )
      filteredBackgroundImages.value = backgroundImages.value
    }
  } catch (error) {
    console.error('加载背景图列表失败:', error)
    ElMessage.error('加载图片列表失败')
  }
}

// 搜索背景图
const searchBackgroundImages = () => {
  if (!backgroundImageSearch.value.trim()) {
    filteredBackgroundImages.value = backgroundImages.value
  } else {
    const keyword = backgroundImageSearch.value.toLowerCase()
    filteredBackgroundImages.value = backgroundImages.value.filter(image =>
      (image.name && image.name.toLowerCase().includes(keyword)) ||
      (image.url && image.url.toLowerCase().includes(keyword))
    )
  }
}

// 刷新背景图列表
const refreshBackgroundImages = () => {
  loadBackgroundImages()
}

// 选择背景图
const selectBackgroundImage = (image) => {
  selectedBackgroundImage.value = image
}

// 确认背景图选择
const confirmBackgroundImageSelection = async () => {
  if (selectedBackgroundImage.value) {
    editLineForm.value.background_url = selectedBackgroundImage.value.url

    // 提取主题色
    try {
      const themeColor = await extractThemeColorFromImage(selectedBackgroundImage.value.url)
      editLineForm.value.bg_theme_color = themeColor
      ElMessage.success('背景图选择成功，已自动提取主题色')
    } catch (error) {
      console.warn('主题色提取失败:', error)
      editLineForm.value.bg_theme_color = ''
      ElMessage.success('背景图选择成功')
    }

    closeBackgroundImageSelector()
  }
}

// 处理背景图上传
const handleBackgroundImageUpload = async (file) => {
  try {
    backgroundImageUploading.value = true

    // 检查文件大小
    checkFileSize(file.raw, 10) // 最大10MB

    // 上传到OSS
    const result = await uploadToOSS(file.raw, 'image', 'script-lines')

    // 更新表单数据
    editLineForm.value.background_url = result.url

    // 提取主题色
    try {
      const themeColor = await extractThemeColorFromImage(result.url)
      editLineForm.value.bg_theme_color = themeColor
      ElMessage.success('背景图上传成功，已自动提取主题色')
    } catch (colorError) {
      console.warn('主题色提取失败:', colorError)
      editLineForm.value.bg_theme_color = ''
      ElMessage.success('背景图上传成功')
    }

  } catch (error) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    backgroundImageUploading.value = false
  }
}

// 清除背景图
const clearBackgroundImage = () => {
  editLineForm.value.background_url = ''
  editLineForm.value.bg_theme_color = ''
}

// 处理手动配音
const handleManualDubbing = async (line) => {
  currentManualDubbingLine.value = line
  manualDubbingForm.value = {
    original_url: '',
    voice_changed_url: '',
    dubbed_url: '',
    character_id: line.character_id || null,
    dubbed_duration: 0,
    selected_audio_type: 'original'
  }

  // 确保角色详细信息已缓存（包括头像）
  if (line.character_id && !charactersCache.value.has(line.character_id)) {
    await batchGetCharacterInfo([line.character_id])
  }

  manualDubbingDialogVisible.value = true
}



// 处理手动配音文件上传
const handleManualDubbingFileChange = async (file) => {
  if (manualDubbingUploading.value) {
    ElMessage.warning('正在上传中，请稍候...')
    return
  }

  try {
    manualDubbingUploading.value = true

    // 检查文件大小 (最大50MB)
    checkFileSize(file.raw, 50)

    // 上传到OSS的/dubbing路径
    const result = await uploadToOSS(file.raw, 'audio', 'dubbing')

    // 获取音频时长
    const duration = await getAudioDuration(file.raw)

    // 更新表单数据 - 保存为原声音频
    manualDubbingForm.value.original_url = result.url
    manualDubbingForm.value.dubbed_duration = Math.round(duration * 1000) // 转换为毫秒

    // 重置变声相关状态
    manualDubbingForm.value.voice_changed_url = ''
    manualDubbingForm.value.selected_audio_type = 'original'

    ElMessage.success('音频上传成功')

  } catch (error) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    manualDubbingUploading.value = false
  }
}

// 获取音频文件时长
const getAudioDuration = (file) => {
  return new Promise((resolve, reject) => {
    const audio = new Audio()
    const url = URL.createObjectURL(file)

    audio.addEventListener('loadedmetadata', () => {
      URL.revokeObjectURL(url)
      resolve(audio.duration)
    })

    audio.addEventListener('error', () => {
      URL.revokeObjectURL(url)
      reject(new Error('无法获取音频时长'))
    })

    audio.src = url
  })
}

// 选择音频类型
const selectAudioType = (audioType) => {
  manualDubbingForm.value.selected_audio_type = audioType
}

// 处理变声
const handleVoiceChangeForDubbing = async () => {
  if (!manualDubbingForm.value.original_url) {
    ElMessage.warning('请先上传原声音频')
    return
  }

  if (!currentManualDubbingLine.value.character_id) {
    ElMessage.warning('该台词未绑定角色，无法进行变声')
    return
  }

  voiceChangeLoading.value = true
  try {
    const env = import.meta.env.VITE_APP_ENV === 'production' ? 'prod' : 'test'

    // 检查是否有角色资源包ID
    if (!currentManualDubbingLine.value.character_asset_id) {
      throw new Error('该台词未绑定角色资源包，无法进行变声')
    }

    const preset_name = import.meta.env.VITE_APP_ENV === 'production'
      ? `${currentManualDubbingLine.value.character_id}_${currentManualDubbingLine.value.character_asset_id}`
      : `test_${currentManualDubbingLine.value.character_id}_${currentManualDubbingLine.value.character_asset_id}`

    console.log('变声preset_name:', preset_name)

    const timestamp = Date.now()
    const uuid = uuidv4()
    const request_id = `${timestamp}-${uuid}`
    const mid = `${timestamp}`

    // 先获取preset
    const presetRes = await getPreset({
      env: env,
      preset_name: preset_name
    })

    console.log('获取preset结果:', presetRes)
    if (presetRes.ret !== 1) {
      throw new Error('获取角色预设失败，请确认角色配置是否正确')
    }

    // 检查预设数据
    if (!presetRes.data?.rvc_name) {
      throw new Error('角色预设中rvc_name为空')
    }
    if (!presetRes.data?.reference_audio_url) {
      throw new Error('角色预设中reference_audio_url为空')
    }

    const use_rvc = true // 默认使用RVC变声
    console.log('变声参数 use_rvc=', use_rvc)

    // 加载模型
    if (use_rvc) {
      const loadModelRes = await loadModel({
        rvc_name: presetRes.data?.rvc_name,
        seed_name: 'whisper_small',
        refer_audio_url: presetRes.data?.reference_audio_url,
        request_id: request_id,
        mid: mid
      })

      if (loadModelRes.ret !== 1) {
        throw new Error('加载变声模型失败')
      }
    }

    // 从原声音频URL获取音频文件并重新上传到变声服务器
    const response = await fetch(manualDubbingForm.value.original_url)
    const blob = await response.blob()
    const file = new File([blob], 'original_audio.wav', { type: 'audio/wav' })

    // 上传到OSS用于变声处理
    const result = await uploadToOSS(file, 'audio', 'dubbing/voice_change')
    const audioUrl = result.url

    if (!audioUrl) {
      throw new Error('上传音频文件失败')
    }

    // 调用变声接口
    const rvcRes = await inferRvc({
      rvc_name: presetRes.data?.rvc_name,
      seed_name: 'whisper_small',
      reference_audio_url: presetRes.data?.reference_audio_url,
      request_id: request_id,
      mid: mid,
      source_audio_url: audioUrl,
      cascaded_use_rvc: use_rvc
    })

    if (rvcRes.ret !== 1) {
      throw new Error('变声处理失败')
    }

    // 保存变声结果
    manualDubbingForm.value.voice_changed_url = rvcRes.data.final_output_url
    manualDubbingForm.value.selected_audio_type = 'voice_changed' // 默认选择变声音频

    ElMessage.success('变声处理完成')

  } catch (error) {
    console.error('变声失败:', error)
    ElMessage.error('变声失败：' + (error.message || '未知错误'))
  } finally {
    voiceChangeLoading.value = false
  }
}



// 关闭手动配音弹窗
const closeManualDubbingDialog = () => {
  manualDubbingDialogVisible.value = false
  currentManualDubbingLine.value = {}
  manualDubbingForm.value = {
    original_url: '',
    voice_changed_url: '',
    dubbed_url: '',
    character_id: null,
    dubbed_duration: 0,
    selected_audio_type: 'original'
  }
  voiceChangeLoading.value = false
}

// 提交手动配音
const submitManualDubbing = async () => {
  // 检查是否有原声音频
  if (!manualDubbingForm.value.original_url) {
    ElMessage.error('请先上传音频文件')
    return
  }

  // 检查角色绑定
  if (!currentManualDubbingLine.value.character_id) {
    ElMessage.error('该台词未绑定角色，无法提交配音')
    return
  }

  try {
    manualDubbingSubmitting.value = true

    // 根据用户选择确定最终提交的音频URL
    let finalAudioUrl = ''
    if (manualDubbingForm.value.voice_changed_url && manualDubbingForm.value.selected_audio_type === 'voice_changed') {
      finalAudioUrl = manualDubbingForm.value.voice_changed_url
    } else {
      finalAudioUrl = manualDubbingForm.value.original_url
    }

    // 从完整URL中提取路径（去除域名）
    if (finalAudioUrl.startsWith('http')) {
      const url = new URL(finalAudioUrl)
      finalAudioUrl = url.pathname
    }

    const res = await manualUploadDubbing({
      line_id: currentManualDubbingLine.value.id,
      character_id: currentManualDubbingLine.value.character_id, // 使用台词绑定的角色ID
      dubbed_url: finalAudioUrl,
      dubbed_duration: manualDubbingForm.value.dubbed_duration
    })

    if (res.code === 0) {
      const audioType = manualDubbingForm.value.selected_audio_type === 'voice_changed' ? '变声' : '原声'
      ElMessage.success(`手动配音上传成功（使用${audioType}音频）`)
      closeManualDubbingDialog()
      // 刷新台词列表
      await getLines()
      // 通知父组件刷新数据
      emit('refresh')
    } else {
      ElMessage.error(res.msg || '手动配音上传失败')
    }
  } catch (error) {
    console.error('手动配音上传失败:', error)
    ElMessage.error('手动配音上传失败，请稍后重试')
  } finally {
    manualDubbingSubmitting.value = false
  }
}

// 提交编辑台词
const submitEditLine = async () => {
  try {
    if (!editLineForm.value.content.trim()) {
      ElMessage.warning('台词内容不能为空')
      return
    }
    editLineLoading.value = true
    const res = await updateLine({
      id: editLineForm.value.id,
      content: editLineForm.value.content,
      background_url: editLineForm.value.background_url || '',
      bg_theme_color: editLineForm.value.bg_theme_color || ''
    })

    if (res.code === 0) {
      ElMessage.success('编辑成功')
      closeEditLineDialog()
      // 刷新台词列表
      await getLines()
      // 通知父组件刷新数据
      emit('refresh')
    } else {
      ElMessage.error(res.msg || '编辑失败')
    }
  } catch (error) {
    console.error('编辑台词失败:', error)
    ElMessage.error('编辑失败，请稍后重试')
  } finally {
    editLineLoading.value = false
  }
}

</script>

<style scoped>

.search-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.pagination-container {
  margin-top: 16px;
  text-align: right;
}

.info-bar {
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  word-break: break-all;
}

.voice-comment {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 确保评论区域的音频播放器有足够宽度 */
.voice-comment :deep(.audio-player) {
  min-width: 280px;
  width: 100%;
}

/* 确保台词列表中的音频播放器有足够宽度 */
.dubbing-info :deep(.audio-player) {
  min-width: 280px;
  width: 100%;
}

.asr-text {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 评论列表样式 */
.comments-list {
  max-height: 60vh;
  overflow-y: auto;
}

.comment-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 16px;
  background: #fff;
}

.main-comment {
  padding: 16px;
}

.comment-header {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 12px;
  color: #666;
}

.comment-id {
  font-weight: bold;
  color: #409eff;
}

.user-info, .character-info {
  color: #606266;
}

.comment-time {
  color: #909399;
  margin-left: auto;
}

.comment-content {
  margin-bottom: 12px;
  line-height: 1.6;
}

.text-content {
  font-size: 14px;
  color: #303133;
  word-break: break-word;
}

.comment-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.content-type {
  color: #909399;
}

.tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.top-tag {
  background: #f56c6c;
  color: white;
}

.hot-tag {
  background: #e6a23c;
  color: white;
}

.author-tag {
  background: #67c23a;
  color: white;
}

.likes {
  color: #f56c6c;
}

.review-status {
  color: #909399;
}

.review-status-container {
  display: inline-block;
  margin-left: 8px;
}

.comment-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.replies-container {
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.reply-item {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  position: relative;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #409eff;
}

.reply-header {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 11px;
  color: #666;
}

.reply-content {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.reply-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  font-size: 11px;
}

.reply-id {
  font-weight: bold;
  color: #409eff;
}

.reply-time {
  color: #909399;
  margin-left: auto;
}

.level-indicator {
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  border: 1px solid #91d5ff;
}

/* 角色信息样式 */
.character-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.character-id {
  font-size: 11px;
  color: #409eff;
  font-weight: bold;
}

.character-name {
  font-size: 12px;
  color: #606266;
  font-weight: normal;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bar .el-input {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }

  .pagination-container {
    text-align: center;
  }

  .info-bar {
    font-size: 14px;
  }

  .reply-item {
    margin-left: 10px;
  }

  /* 表格在移动端的优化 */
  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .cell) {
    padding: 4px 8px;
    word-break: break-all;
  }

  :deep(.el-button--small) {
    padding: 2px 6px;
    font-size: 11px;
  }
}

/* 平板适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .search-bar .el-input {
    width: 250px !important;
  }

  :deep(.el-table) {
    font-size: 13px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  :deep(.el-dialog__body) {
    padding: 10px;
  }

  :deep(.el-table .el-table__cell) {
    padding: 4px 2px;
  }

  .info-bar {
    padding: 8px;
    font-size: 12px;
  }

  .voice-comment {
    gap: 4px;
  }

  .asr-text {
    font-size: 11px;
    padding: 2px 4px;
  }
}

.edit-line-container {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 手动配音上传容器样式 */
.audio-upload-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-button-area {
  display: flex;
  align-items: center;
}

.audio-player-area {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-top: 8px;
}

/* 确保音频播放器有足够的宽度显示播放条 */
.audio-player-area :deep(.audio-player) {
  width: 100%;
  min-width: 350px;
}

/* 音频播放器内部样式优化 */
.audio-player-area :deep(.player-controls) {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.audio-player-area :deep(.progress-container) {
  flex: 1;
  min-width: 220px;
}

.audio-player-area :deep(.progress-slider) {
  min-width: 150px;
}

/* 防止点击事件冒泡到上传组件 */
.audio-player-area {
  pointer-events: auto;
  position: relative;
  z-index: 1;
}

.audio-player-area * {
  pointer-events: auto;
}

/* 上传组件样式优化 */
.audio-uploader :deep(.el-upload) {
  display: inline-block;
}

/* 手动配音弹窗样式 */
.manual-dubbing-container {
  padding: 4px 0;
}

/* 音频预览容器 */
.audio-preview-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.audio-preview-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 角色选择器样式 */
.character-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.character-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.character-avatar-placeholder {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.character-avatar-placeholder .el-icon {
  font-size: 14px;
  color: #999;
}

.character-name {
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.character-id {
  font-size: 12px;
  color: #909399;
  margin-left: auto;
}

.character-tip {
  margin-top: 8px;
}

/* 音频播放器样式优化 */
.single-audio-wrapper {
  width: 100%;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.original-audio {
  border-color: #bfdbfe;
  background-color: #f0f9ff;
}

.dubbed-audio {
  border-color: #bbf7d0;
  background-color: #f0fdf4;
}

.audio-player-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  min-height: 40px;
}

.audio-player-container :deep(.audio-player) {
  flex: 1;
  max-width: 220px;
  min-width: 180px;
}

.audio-duration {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  min-width: 50px;
  text-align: right;
  flex-shrink: 0;
  padding-left: 4px;
}

.no-audio-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  min-height: 40px;
}

.no-audio-text {
  color: #C0C4CC;
  font-style: italic;
  font-size: 13px;
}

.no-content {
  color: #C0C4CC;
  font-style: italic;
  text-align: center;
}
</style>